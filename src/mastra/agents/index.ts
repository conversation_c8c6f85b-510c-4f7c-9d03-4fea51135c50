import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { Memory } from "@mastra/memory";
import {
  searchProductsTool,
  getProductDetailsTool,
  trackOrderTool,
  humanHandoffTool,
  getFaqsTool,
  detectSpamTool,
  getOrderByCodeTool,
  updateOrderStatusTool,
  cancelOrderTool,
  getPromotionsTool,
  createOrderTool,
  sendImagesTool,
} from "../tools";
import {
  fullSystemPrompt,
  moolyExpertPrompt,
  workingMemoryTemplate,
} from "../prompts/system";
import {
  buildInstruction,
  replaceContextVariables,
  contextTemplates,
  geminiOptimizedConfig
} from "../prompts/gemini-optimized-instructions";
import dotenv from "dotenv";
import { PgVector } from "@mastra/pg";
import { google, GoogleGenerativeAIProviderOptions } from "@ai-sdk/google";
import postgresStore from "../utils/postgres-store";
import { TokenLim<PERSON>, ToolCallFilter } from "@mastra/memory/processors";
import { GeminiSystemFixProcessor } from "../processors";
import { CoreMessage, MemoryProcessor } from "@mastra/core";

dotenv.config();

/**
 * Get primary model with fallback
 */
const getPrimaryModel = () => {
  try {
    const modelName = process.env.LLM_MODEL || "gemini-2.0-flash-exp";
    return google(modelName);
  } catch (error) {
    return google("gemini-2.0-flash");
  }
};

const pgVectorClient = new PgVector({
  connectionString: process.env.PG_CONNECTION_STRING ||
    "postgresql://postgres:postgres@localhost:5432/postgres"
});

class RecentMessagesProcessor extends MemoryProcessor {
  private limit: number;

  constructor(limit: number = 10) {
    super({ name: "RecentMessagesProcessor" });
    this.limit = limit;
  }

  process(messages: CoreMessage[]): CoreMessage[] {
    // Lọc thinking và chỉ giữ final_answer cho assistant messages
    const filteredMessages = messages.map(message => {
      if (message.role === 'assistant') {
        // Xử lý content dạng string
        if (typeof message.content === 'string') {
          const finalAnswerMatch = message.content.match(/<final_answer>\s*([\s\S]*?)\s*<\/final_answer>/i);
          if (finalAnswerMatch && finalAnswerMatch[1]) {
            return {
              ...message,
              content: finalAnswerMatch[1].trim()
            };
          }
        }

        // Xử lý content dạng array object
        if (Array.isArray(message.content)) {
          const filteredContent = message.content.map(part => {
            if (part.type === 'text') {
              const finalAnswerMatch = part.text.match(/<final_answer>\s*([\s\S]*?)\s*<\/final_answer>/i);
              if (finalAnswerMatch && finalAnswerMatch[1]) {
                return {
                  ...part,
                  text: finalAnswerMatch[1].trim()
                };
              }
            }
            return part;
          });

          return {
            ...message,
            content: filteredContent
          };
        }
      }
      return message;
    });
    // Keep only the most recent messages
    return filteredMessages.slice(-this.limit);
  }
}


// Initialize memory with PostgreSQL storage
const memory = new Memory({
  storage: postgresStore,

  // Sử dụng PgVector làm vector database cho semantic search
  // vector: pgVectorClient,
  // embedder: openai.embedding("text-embedding-3-small"),

  // Thêm processors để xử lý messages và tránh lỗi empty content
  processors: [
    // new TokenLimiter(5000),
    new RecentMessagesProcessor(30),
  ],

  options: {
    lastMessages: 30,
    semanticRecall: false,
    // threads: {
    //   generateTitle: true,
    // },
    // workingMemory: {
    //   enabled: true,
    //   template: workingMemoryTemplate,
    // },
  },
});

// const newMemory = new Memory({
//   storage: postgresStore
// })

// ===== OPTIMIZED GEMINI API CUSTOM INSTRUCTION =====
// Sử dụng template được tối ưu hóa theo best practices của Google Gemini API
const businessContext = replaceContextVariables(contextTemplates.ecommerce, {
  COMPANY_NAME: "Thọ Trần Shop",
  INDUSTRY: "Bán lẻ đồ dùng trẻ em trực tuyến",
  PRODUCTS: "Quần áo, Giày dép, Phụ kiện, Đồ chơi giáo dục, Dụng cụ học tập (3-5 tuổi)",
  FEATURES: "Cotton 100%, An toàn, Thiết kế cute, Size đa dạng",
  PRICING: "20,000 - 250,000 VNĐ",
  SHIPPING: "Miễn phí từ 300k, Giao 2-3 ngày, Ship toàn quốc, COD có sẵn",
  RETURNS: "Đổi trả miễn phí 7 ngày, Hoàn tiền 100% nếu lỗi shop",
  CONTACT: "Chat 8h-22h, Hotline: 0901.234.567, Email: <EMAIL>",
  OFFERS: "Giảm 10% đơn đầu, Mua 2 tặng 1 đồ chơi, Tích điểm đổi quà, Flash sale cuối tuần"
});

const exampleResponses = `
## EXAMPLES

**Example 1 - Product Inquiry:**
Customer: "Có áo thun cho bé gái 4 tuổi không?"
Response:
<thinking>
Khách hỏi về áo thun cho bé gái 4 tuổi. Đây là sản phẩm chính của shop, nằm trong độ tuổi target 3-5 tuổi. Tôi có thể trả lời dựa trên thông tin sản phẩm và nên sử dụng tool tìm kiếm để cung cấp thông tin cụ thể.
</thinking>

<final_answer>
Có nhiều mẫu áo thun xinh cho bé gái 4 tuổi! Chất liệu cotton 100% an toàn, thiết kế cute với nhiều màu sắc tươi sáng. Giá từ 50,000 - 200,000 VNĐ. Để tìm mẫu phù hợp nhất, cho mình biết bé thích màu gì và phong cách nào nhé?
</final_answer>

**Example 2 - Order Tracking:**
Customer: "Đơn hàng DH001 của em đến đâu rồi ạ?"
Response:
<thinking>
Khách hàng hỏi về tình trạng đơn hàng với mã DH001. Tôi cần sử dụng tool trackOrderTool để kiểm tra thông tin chính xác.
</thinking>

<final_answer>
Để kiểm tra tình trạng đơn hàng DH001, mình sẽ tra cứu ngay cho bạn nhé!
</final_answer>

**Example 3 - Return Policy:**
Customer: "Mua rồi có đổi được không?"
Response:
<thinking>
Khách hỏi về chính sách đổi trả. Thông tin này có sẵn trong business context: đổi trả miễn phí trong 7 ngày, hoàn tiền 100% nếu lỗi shop, chỉ đổi hàng chưa sử dụng còn tag.
</thinking>

<final_answer>
Được đổi trả miễn phí trong 7 ngày nha! Điều kiện là hàng chưa qua sử dụng và còn tag. Nếu lỗi từ shop thì hoàn tiền 100% luôn. Bạn gặp vấn đề gì với sản phẩm vậy?
</final_answer>
`;

// Build instruction với template được tối ưu hóa
const customInstruction = buildInstruction(
  `# ROLE & IDENTITY
Bạn là Trợ lý AI Chuyên nghiệp của Thọ Trần Shop - chuyên gia tư vấn đồ dùng trẻ em trực tuyến.

## CORE CAPABILITIES
- Tư vấn sản phẩm chính xác và phù hợp
- Hỗ trợ đặt hàng và theo dõi đơn hàng
- Giải đáp thắc mắc về chính sách và dịch vụ
- Xử lý khiếu nại và hỗ trợ khách hàng

## RESPONSE FORMAT
Luôn trả lời theo cấu trúc:

<thinking>
[Phân tích yêu cầu khách hàng, kiểm tra thông tin có sẵn, quyết định có cần dùng tool không]
</thinking>

<final_answer>
[Câu trả lời trực tiếp, tự nhiên, không đề cập đến "thông tin được cung cấp" hay "bối cảnh"]
</final_answer>

## COMMUNICATION GUIDELINES
✅ DO:
- Trả lời trực tiếp, ngắn gọn, tập trung vào vấn đề chính
- Sử dụng ngôn ngữ khách hàng đang dùng
- Xưng hô phù hợp với giới tính khách hàng
- Chủ động sử dụng tools khi cần thiết
- Ghi nhớ lịch sử hội thoại để tránh hỏi lại

❌ DON'T:
- Chào hỏi dài dòng
- Lặp lại "ạ, dạ" quá nhiều
- Đề cập "theo thông tin được cung cấp"
- Hỏi lại thông tin đã biết`,
  businessContext,
  exampleResponses
);

// ===== OPTIMIZED GEMINI AGENT CONFIGURATION =====
// Tối ưu hóa theo best practices của Google Gemini API
export const ecommerceAgent = new Agent({
  name: "E-commerce Assistant Agent",
  instructions: `${customInstruction}`,

  // Sử dụng model ổn định giống rag agent
  model: google('gemini-2.5-flash-lite-preview-06-17'),

  // Tools được tối ưu hóa cho customer service
  tools: ({
    searchProductsTool,
    getProductDetailsTool,
    trackOrderTool,
    humanHandoffTool,
    getFaqsTool,
    detectSpamTool,
    getOrderByCodeTool,
    updateOrderStatusTool,
    cancelOrderTool,
    getPromotionsTool,
    createOrderTool,
    sendImagesTool,
  }),

  // Memory system được tối ưu
  memory,

  // Cấu hình đơn giản hơn để tránh lỗi JSON response
  defaultGenerateOptions: {
    maxRetries: 2,
    temperature: 0.2,
    maxSteps: 5,
    telemetry: {
      isEnabled: true
    }
  },
  defaultStreamOptions: {
    maxRetries: 2,
    temperature: 0.2,
    maxSteps: 5,
    telemetry: {
      isEnabled: true
    },
    // providerOptions: {
    //   google: {
    //     thinkingConfig: {
    //       thinkingBudget: 1000,
    //       includeThoughts: true
    //     },
    //   } satisfies GoogleGenerativeAIProviderOptions,
    // },
  }
});

// Export các agents từ loma-customer-service
export { ragAgent, keywordAnalysisAgent } from './loma-customer-service';
